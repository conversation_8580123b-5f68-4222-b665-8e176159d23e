# Nav2融合配置文件 - 使用PolarBear PID控制器
# 基于nav2_fusion.yaml，将DWB控制器替换为pb_omni_pid_pursuit_controller
# {{ AURA-X: Create - 使用pb_omni_pid_pursuit_controller的融合导航配置. Approval: 寸止(ID:pb_controller_integration). }}

# ========== 行为树导航器配置 ==========
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_footprint
    odom_topic: /odom
    bt_loop_duration: 10
    default_server_timeout: 20
    
    # 使用标准的行为树
    default_nav_to_pose_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_to_pose_w_replanning_and_recovery.xml"
    default_nav_through_poses_bt_xml: "/opt/ros/humble/share/nav2_bt_navigator/behavior_trees/navigate_through_poses_w_replanning_and_recovery.xml"
    
    # 插件配置
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node

# ========== 控制器服务器配置 ==========
# 使用pb_omni_pid_pursuit_controller替代DWB控制器
controller_server:
  ros__parameters:
    use_sim_time: false
    odom_topic: odom
    controller_frequency: 20.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0  #最小速度阈值
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    progress_checker_plugins: ["progress_checker"]
    goal_checker_plugins: ["goal_checker"]
    controller_plugins: ["FollowPath"]

    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.5
      # {{ AURA-X: Modify - 缩短等待时间，更快触发恢复行为. Approval: 寸止(ID:faster_recovery). }}
      movement_time_allowance: 5.0         # 从10秒缩短到5秒

    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      # {{ AURA-X: Modify - 调整目标容差适配大车体. Approval: 寸止(ID:large_robot_footprint). }}
      xy_goal_tolerance: 0.2              # 增加位置容差适配大车体
      yaw_goal_tolerance: 0.2             # 增加角度容差
      stateful: True
      
    FollowPath:
      # {{ AURA-X: Modify - 使用pb_omni_pid_pursuit_controller获得更高精度. Approval: 寸止(ID:pb_controller_integration). }}
      plugin: "pb_omni_pid_pursuit_controller::OmniPidPursuitController"
      
      # === PID控制参数 ===
      # {{ AURA-X: Modify - 降低PID增益减少左摇右摆. Approval: 寸止(ID:reduce_oscillation). }}
      # 位置PID控制器（控制到目标点的距离）
      translation_kp: 1.2          # 大幅降低比例增益，减少过冲和摆动
      translation_ki: 0.01         # 进一步降低积分增益，避免积分饱和
      translation_kd: 0.5          # 增加微分增益，提供更强阻尼

      # 姿态PID控制器（控制朝向角度）
      enable_rotation: true
      rotation_kp: 0.6             # 大幅降低旋转比例增益，减少角度摆动
      rotation_ki: 0.005           # 大幅降低积分增益，避免角度震荡
      rotation_kd: 0.8             # 增加微分增益，提供更强角度阻尼
      
      # === 差分驱动专用参数 ===
      # {{ AURA-X: Modify - 进一步降低旋转增益，减少角度过冲. Approval: 寸止(ID:fix_rotation_overshoot). }}
      differential_angle_threshold: 0.436    # 25度，更早开始旋转调整
      differential_rotation_gain: 0.8        # 进一步降低旋转增益，减少过冲
      differential_speed_reduction: 0.5      # 大角度时速度降低更多

      
      # === 前瞻距离配置 ===
      # {{ AURA-X: Modify - 降低前瞻距离减少路径摆动. Approval: 寸止(ID:reduce_oscillation). }}
      lookahead_dist: 0.8                    # 降低基础前瞻距离，减少摆动
      use_velocity_scaled_lookahead_dist: true
      lookahead_time: 1.8                    # 降低前瞻时间，提高响应性
      min_lookahead_dist: 0.5                # 降低最小前瞻距离
      max_lookahead_dist: 1.5                # 降低最大前瞻距离
      
      # === 速度限制 ===
      # {{ AURA-X: Modify - 降低速度限制适配大型机器人. Approval: 寸止(ID:large_robot_safety). }}
      v_linear_min: -0.2                     # 最小线速度（允许倒退，降低）
      v_linear_max: 0.3                      # 最大线速度（从0.6降至0.4）
      v_angular_min: -0.3                    # 最小角速度（从-1.2降至-0.8）
      v_angular_max: 0.5                     # 最大角速度（从1.2降至0.8）
      
      # === 路径跟踪配置 ===
      # {{ AURA-X: Modify - 优化路径跟踪减少摆动. Approval: 寸止(ID:reduce_oscillation). }}
      transform_tolerance: 0.1
      min_max_sum_error: 1.0
      use_interpolation: true
      use_rotate_to_heading: false          # 关闭朝向调整，减少不必要的旋转摆动
      use_rotate_to_heading_treshold: 0.2   # 增加旋转调整角度阈值
      
      # === 接近目标配置 ===
      # {{ AURA-X: Modify - 调整接近目标参数适配大车体. Approval: 寸止(ID:large_robot_footprint). }}
      min_approach_linear_velocity: 0.08     # 降低接近目标时最小速度
      approach_velocity_scaling_dist: 0.6    # 增加开始减速的距离，1.0->0.6      
      # === 曲率限制配置 ===
      # {{ AURA-X: Modify - 调整曲率限制适配大车体转弯. Approval: 寸止(ID:large_robot_footprint). }}
      curvature_min: 0.2                     # 降低最小曲率阈值，大车体更早减速
      curvature_max: 0.4                     # 降低最大曲率阈值，更保守的转弯
      reduction_ratio_at_high_curvature: 0.3 # 高曲率时速度降低更多
      curvature_forward_dist: 0.4            # 增加前向曲率计算距离
      curvature_backward_dist: 0.3           # 增加后向曲率计算距离
      max_velocity_scaling_factor_rate: 1.5  # 降低速度缩放变化率，更平滑
      
      # === 其他配置 ===
      max_robot_pose_search_dist: 10.0       # 最大机器人位姿搜索距离

# ========== 局部代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
local_costmap:
  local_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 20.0              # 地图更新频率(Hz)，高频率保证实时性
      publish_frequency: 20.0             # 地图发布频率(Hz)
      global_frame: odom                   # 全局参考坐标系(里程计坐标系，标准名称)
      robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间
      rolling_window: true                 # 启用滚动窗口，跟随机器人移动

      # --- 地图尺寸配置 ---
      width: 4                             # 增加地图宽度适配大车体和膨胀半径
      height: 4                            # 增加地图高度适配大车体和膨胀半径
      resolution: 0.03                     # 地图分辨率(m/pixel)，与建图分辨率一致

      # --- 机器人形状配置 ---
      # {{ AURA-X: Modify - 更新机器人轮廓适配750mm×600mm车体. Approval: 寸止(ID:large_robot_footprint). }}
      # 使用矩形footprint，基于实际尺寸750mm×600mm + 安全边距50mm
      footprint: "[[0.4, 0.325], [0.4, -0.325], [-0.4, -0.325], [-0.4, 0.325]]"

      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]

      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题

      # === 障碍物层配置 ===
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        footprint_clearing_enabled: true   # 启用机器人轮廓清除
        combination_method: 1              # 组合方法：1取最大值 减少噪声影响
        scan:
          topic: merged                    # 激光雷达话题（相对路径，支持命名空间）
          max_obstacle_height: 2.0         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)

      # === 膨胀层配置 ===
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        # {{ AURA-X: Modify - 调整膨胀半径适配750mm×600mm车体. Approval: 寸止(ID:large_robot_footprint). }}
        cost_scaling_factor: 6.0           # 进一步增加代价缩放因子
        inflation_radius: 0.3              # 增加膨胀半径适配大车体（车体对角线约0.96m）

      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 全局代价地图配置 ==========
# 关键成功要素：作为独立节点在lifecycle_manager中管理
global_costmap:
  global_costmap:
    ros__parameters:
      # --- 基本参数配置 ---
      update_frequency: 10.0              # 地图更新频率(Hz)，比局部地图低以节省计算
      publish_frequency: 10.0             # 地图发布频率(Hz)
      global_frame: map                    # 全局参考坐标系(地图坐标系)
      robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
      transform_timeout: 1.0               # TF变换超时时间(秒)，增加容错性
      tf_buffer_duration: 10.0             # TF缓存持续时间(秒)
      use_sim_time: false                  # 使用真实时间

      # --- 机器人形状配置 ---
      # {{ AURA-X: Modify - 更新全局代价地图机器人轮廓. Approval: 寸止(ID:large_robot_footprint). }}
      # 与局部代价地图保持一致的矩形footprint，750mm×600mm + 安全边距
      footprint: "[[0.4, 0.325], [0.4, -0.325], [-0.4, -0.325], [-0.4, 0.325]]"

      # --- 地图参数配置 ---
      resolution: 0.03                     # 地图分辨率(m/pixel)，与Cartographer建图一致
      track_unknown_space: false           # 不跟踪未知空间，提高性能

      # --- 插件配置 ---
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]

      # === 静态层配置 ===
      # 成功关键：直接订阅Cartographer发布的/map话题
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        map_subscribe_transient_local: True  # 订阅持久化地图话题

      # === 障碍物层配置 ===
      # 与局部代价地图类似，但用于全局路径规划
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True                      # 启用障碍物层
        observation_sources: scan          # 观测数据源
        scan:
          topic: merged                    # 激光雷达话题（相对路径，支持命名空间）
          max_obstacle_height: 2.0         # 最大障碍物高度(m)
          clearing: True                   # 启用障碍物清除
          marking: True                    # 启用障碍物标记
          data_type: "LaserScan"           # 数据类型
          raytrace_max_range: 3.0          # 光线追踪最大范围(m)
          raytrace_min_range: 0.0          # 光线追踪最小范围(m)
          obstacle_max_range: 2.5          # 障碍物检测最大范围(m)
          obstacle_min_range: 0.0          # 障碍物检测最小范围(m)

      # === 膨胀层配置 ===
      # {{ AURA-X: Modify - 优化全局膨胀参数生成更直路径. Approval: 寸止(ID:reduce_path_deviation). }}
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true                      # 启用膨胀层
        # {{ AURA-X: Modify - 调整全局膨胀半径适配大车体. Approval: 寸止(ID:large_robot_footprint). }}
        cost_scaling_factor: 6.0           # 进一步增加代价缩放因子
        inflation_radius: 0.8              # 增加膨胀半径，比局部地图大0.1m

      always_send_full_costmap: True       # 总是发送完整代价地图

# ========== 地图服务器配置 ==========
# Map Server负责加载和发布静态地图，由launch文件动态配置
map_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    yaml_filename: ""                     # 将由launch文件动态设置

# ========== 规划器服务器配置 ==========
# Planner Server负责全局路径规划，从起点到终点生成最优路径
planner_server:
  ros__parameters:
    expected_planner_frequency: 10.0      # 期望的规划频率(Hz)
    use_sim_time: false                   # 使用真实时间
    planner_plugins: ["GridBased"]        # 规划器插件列表

    # === SMAC 2D规划器配置 ===
    # 适用于配合pb_omni_pid_pursuit_controller使用的2D规划算法
    GridBased:
      plugin: "nav2_smac_planner/SmacPlanner2D"

      # --- 基本配置 ---
      tolerance: 0.15                      # 目标容差(m)，增加容差提高成功率
      downsample_costmap: false           # 不降采样代价地图，保持精度
      downsampling_factor: 1              # 降采样因子(未使用)
      allow_unknown: false                 # 允许通过未知区域，提高规划成功率
      max_iterations: 1000000             # 最大迭代次数
      max_on_approach_iterations: 1000    # 接近目标时的最大迭代次数
      max_planning_time: 3.0              # 最大规划时间(秒)

      # --- 运动模型配置 ---
      motion_model_for_search: "MOORE"    # 使用Moore邻域(8方向)，适合2D规划
      angle_quantization_bins: 72         # 角度量化分箱数(5度精度)

      # --- 代价惩罚配置 ---
      cost_penalty: 3.0                   # 代价惩罚，避免接近障碍物

      # --- 性能优化配置 ---
      use_final_approach_orientation: true  # 暂时关闭最终朝向要求
      smooth_path: true                   # 启用路径平滑

# ========== 路径平滑器服务器配置 ==========
# Smoother Server负责对规划的路径进行平滑处理，提高执行质量
smoother_server:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoother_plugins: ["simple_smoother"]  # 使用简单平滑器

    # === 简单平滑器配置 ===
    # 基于简单算法的路径平滑，计算量小，适合实时应用
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"

      # --- 基本配置 ---
      tolerance: 1.0e-10                  # 收敛容差
      max_its: 1000                       # 最大迭代次数
      do_refinement: true                 # 启用路径细化

      # --- 平滑参数 ---
      w_data: 0.2                         # 数据权重，降低以允许更多平滑
      w_smooth: 0.3                       # 平滑权重，增加以获得更平滑路径

# ========== 行为服务器配置 ==========
# Behavior Server负责执行恢复行为，当导航失败时进行恢复操作
behavior_server:
  ros__parameters:
    # --- 基本配置 ---
    use_sim_time: false                   # 使用真实时间
    local_costmap_topic: local_costmap/costmap_raw     # 局部代价地图话题
    global_costmap_topic: global_costmap/costmap_raw   # 全局代价地图话题
    local_footprint_topic: local_costmap/published_footprint   # 局部机器人轮廓话题
    global_footprint_topic: global_costmap/published_footprint # 全局机器人轮廓话题
    cycle_frequency: 10.0                # 行为执行频率(Hz)
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]  # 行为插件列表

    # === 行为插件配置 ===
    spin:
      plugin: "nav2_behaviors/Spin"       # 原地旋转行为
    backup:
      plugin: "nav2_behaviors/BackUp"     # 后退行为
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"  # 按航向行驶行为
    wait:
      plugin: "nav2_behaviors/Wait"       # 等待行为

    # --- 行为参数配置 ---
    local_frame: odom                     # 局部坐标系（标准名称）
    global_frame: map                     # 全局坐标系（标准名称）
    robot_base_frame: base_footprint     # 机器人本体坐标系（标准名称）
    transform_tolerance: 0.4              # TF变换容差(秒)
    simulate_ahead_time: 2.0              # 前瞻仿真时间(秒)
    max_rotational_vel: 0.4                # 最大旋转速度(rad/s)
    min_rotational_vel: 0.2               # 最小旋转速度(rad/s)
    rotational_acc_lim: 0.6               # 旋转加速度限制(rad/s²)

# ========== 路径点跟随器配置 ==========
# Waypoint Follower负责执行多点导航任务
waypoint_follower:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    loop_rate: 20                         # 循环频率(Hz)
    stop_on_failure: false                # 失败时不停止，继续下一个路径点
    waypoint_task_executor_plugin: "wait_at_waypoint"  # 路径点任务执行器插件

    # === 路径点等待任务配置 ===
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True                       # 启用等待任务
      waypoint_pause_duration: 2000      # 在每个路径点停留2秒(毫秒)

# ========== 速度平滑器配置 ==========
# Velocity Smoother负责对控制命令进行平滑处理，提高机器人运动质量
velocity_smoother:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    smoothing_frequency: 20.0             # 平滑频率(Hz)
    scale_velocities: false               # 不缩放速度
    feedback: "OPEN_LOOP"                 # 开环反馈模式

    # --- 速度限制配置 ---
    # {{ AURA-X: Modify - 进一步优化速度参数减少摆动. Approval: 寸止(ID:reduce_oscillation). }}
    max_velocity: [0.25, 0.0, 0.4]       # 进一步降低最大速度，提高稳定性
    min_velocity: [0.0, 0.0, -0.25]      # 降低最小角速度
    max_accel: [0.2, 0.0, 0.4]           # 降低最大加速度，更平滑启动
    max_decel: [-0.2, 0.0, -0.4]         # 降低最大减速度，更平滑停止

    # --- 其他配置 ---
    odom_topic: "odom"                    # 里程计话题
    odom_duration: 0.1                    # 里程计持续时间(秒)
    deadband_velocity: [0.0, 0.0, 0.0]   # 死区速度
    velocity_timeout: 1.0                 # 速度超时时间(秒)

# ========== Cartographer节点配置 ==========
cartographer_node:
  ros__parameters:
    use_sim_time: false

# Cartographer占用栅格节点配置
cartographer_occupancy_grid_node:
  ros__parameters:
    use_sim_time: false
