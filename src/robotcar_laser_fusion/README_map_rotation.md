# 地图旋转节点 (Map Rotation Node)

## 功能概述

地图旋转节点是一个ROS2节点，用于自动修正Cartographer建图过程中的地图倾斜问题。该节点通过分析地图中的最长直线特征，自动计算并应用旋转变换，确保建出的地图不歪斜。

## 核心特性

- **自动倾斜检测**: 使用OpenCV的Canny边缘检测和Hough直线检测算法
- **实时地图修正**: 订阅`carto_map`话题，发布修正后的`map`话题
- **TF变换管理**: 自动维护`map`到`carto_map`的坐标变换
- **可视化支持**: 发布最长线段的可视化标记到`current_longest`话题
- **智能更新**: 只有在检测到显著变化时才更新旋转角度

## 话题接口

### 订阅话题
- `carto_map` (nav_msgs/OccupancyGrid): Cartographer原始地图数据

### 发布话题
- `map` (nav_msgs/OccupancyGrid): 修正后的地图数据
- `current_longest` (visualization_msgs/Marker): 检测到的最长线段可视化
- `/tf` (tf2_msgs/TFMessage): map->carto_map坐标变换

## 算法原理

1. **地图预处理**: 将占用栅格地图转换为OpenCV图像格式
2. **边缘检测**: 使用Canny算法检测地图中的边缘特征
3. **直线检测**: 使用Hough变换检测图像中的直线段
4. **角度计算**: 找到最长的直线段，计算其与水平轴的夹角
5. **地图旋转**: 根据计算出的角度对整个地图进行旋转变换
6. **坐标变换**: 更新TF树中的坐标变换关系

## 参数配置

### 核心参数
- **更新阈值**: 当检测到的最长线段长度变化超过10%或角度变化超过1度时触发更新
- **边缘检测参数**: Canny算法阈值为75-99
- **直线检测参数**: Hough变换最小线段长度为27像素，最大间隙为10像素

### 定时器配置
- **地图处理定时器**: 5秒间隔，处理地图旋转
- **TF发布定时器**: 20毫秒间隔，发布坐标变换

## 集成方式

### 在建图启动文件中的集成

地图旋转节点已集成到`fusion_03_mapping.launch.py`中：

```python
# 地图旋转节点 - 7秒后启动，等待占用栅格节点稳定
map_rotation_node = TimerAction(
    period=7.0,
    actions=[
        GroupAction([
            PushRosNamespace(namespace=namespace),
            Node(
                package='robotcar_laser_fusion',
                executable='map_rotation_node',
                name='map_rotation_node',
                output='screen',
                parameters=[{'use_sim_time': use_sim_time}],
                remappings=[
                    ('carto_map', 'carto_map'),
                    ('map', 'map'),
                    ('current_longest', 'current_longest'),
                ]
            )
        ])
    ]
)
```

### 启动顺序
1. **0秒**: 基础硬件和双雷达融合系统
2. **3秒**: Cartographer SLAM节点
3. **5秒**: Cartographer占用栅格节点 (输出到`carto_map`)
4. **7秒**: 地图旋转节点 (订阅`carto_map`，发布`map`)

## 使用方法

### 启动完整建图系统
```bash
ros2 launch robotcar_laser_fusion fusion_03_mapping.launch.py
```

### 单独启动地图旋转节点
```bash
ros2 run robotcar_laser_fusion map_rotation_node
```

### 查看话题状态
```bash
# 查看原始地图
ros2 topic echo /carto_map

# 查看修正后的地图
ros2 topic echo /map

# 查看可视化标记
ros2 topic echo /current_longest
```

### 在RViz中可视化
1. 添加Map显示插件，话题设置为`/map`
2. 添加Marker显示插件，话题设置为`/current_longest`
3. 可以同时显示`/carto_map`进行对比

## 技术细节

### 坐标系关系
- `map`: 修正后的全局地图坐标系
- `carto_map`: Cartographer原始地图坐标系
- TF链: `map` -> `carto_map` -> `odom` -> `base_footprint`

### 性能优化
- 使用互斥锁保护地图数据访问
- 只在地图更新时进行处理
- 智能阈值避免频繁更新

### 错误处理
- 当检测不到直线时保持当前角度
- 自动处理地图尺寸变化
- 边界检查防止数组越界

## 故障排除

### 常见问题

1. **节点无法启动**
   - 检查OpenCV和Eigen依赖是否正确安装
   - 确认package.xml中的依赖配置

2. **地图不旋转**
   - 检查是否有`carto_map`话题发布
   - 确认地图中是否有足够的直线特征

3. **旋转角度不准确**
   - 调整Canny和Hough参数
   - 检查建图环境是否有明显的直线特征

### 调试命令
```bash
# 检查话题连接
ros2 topic list | grep map

# 查看节点日志
ros2 log set_logger_level map_rotation_node DEBUG

# 检查TF变换
ros2 run tf2_tools view_frames
```

## 版本信息
- 版本: 1.0.0
- 兼容ROS2: Humble及以上
- 依赖: OpenCV 4.x, Eigen3, Cartographer
