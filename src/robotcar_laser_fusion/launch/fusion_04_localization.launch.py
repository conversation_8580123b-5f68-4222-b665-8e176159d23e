import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, ExecuteProcess, GroupAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition

from launch_ros.actions import Node, PushRosNamespace, SetRemap


def generate_launch_description():

    pkg_robotcar_fusion = get_package_share_directory('robotcar_laser_fusion')
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')

    use_sim_time = LaunchConfiguration('use_sim_time')
    start_rviz = LaunchConfiguration('start_rviz')
    use_ekf = LaunchConfiguration('use_ekf')
    map_file = LaunchConfiguration('map_file')
    namespace = LaunchConfiguration('namespace')

    # === 1. 启动基础硬件和双雷达融合系统 ===
    fusion_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_01_bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 2. 启动EKF传感器融合（可选） ===
    ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_02_ekf.launch.py')
        ),
        condition=IfCondition(use_ekf),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 3. 启动Cartographer定位系统 ===
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_localization.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'rviz.rviz'])

    cartographer_localization_node = TimerAction(
        period=5.0,  # 等待融合系统完全启动
        actions=[
            GroupAction([
                PushRosNamespace(namespace=namespace),
                SetRemap("/tf", "tf"),
                SetRemap("/tf_static", "tf_static"),
                Node(
                    package='cartographer_ros',
                    executable='cartographer_node',
                    name='cartographer_node',
                    output='screen',
                    parameters=[
                        {'use_sim_time': use_sim_time},
                    ],
                    arguments=[
                        '-configuration_directory', cartographer_config_dir,
                        '-configuration_basename', cartographer_config_basename,
                        '-load_state_filename', map_file
                    ],
                    remappings=[
                        ('scan', 'merged'),  # 使用融合后的激光雷达数据
                        ('imu', 'imu_sensor_broadcaster/imu'),
                        ('odom', 'odom'),
                    ]
                )
            ])
        ]
    )

    # === 4. 启动Cartographer占用栅格节点 ===
    occupancy_grid_node = TimerAction(
        period=4.0,  # 等待Cartographer节点启动
        actions=[
            GroupAction([
                PushRosNamespace(namespace=namespace),
                SetRemap("/tf", "tf"),
                SetRemap("/tf_static", "tf_static"),
                Node(
                    package='cartographer_ros',
                    executable='cartographer_occupancy_grid_node',
                    name='cartographer_occupancy_grid_node',
                    output='screen',
                    parameters=[{'use_sim_time': use_sim_time}],
                    arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
                )
            ])
        ]
    )

    # === 5. 启动RViz可视化 ===
    localization_rviz_node = TimerAction(
        period=5.0,  # 等待所有节点启动
        actions=[
            ExecuteProcess(
                cmd=['rviz2', '-d', rviz_config_file],
                output='screen',
                condition=IfCondition(start_rviz),
                additional_env={'DISPLAY': ':0'}
            )
        ]
    )

    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='False',  # 修复：使用Python布尔值格式
            description='Use simulation time if true'
        ),

        DeclareLaunchArgument(
            'start_rviz',
            default_value='False',  # 默认不启动RViz
            description='Start RViz for localization visualization'
        ),

        DeclareLaunchArgument(
            'use_ekf',
            default_value='True',
            description='Use EKF for sensor fusion'
        ),

        DeclareLaunchArgument(
            'map_file',
            default_value='/home/<USER>/test_ws/src/robotcar_nav/maps/0804.pbstream',  # 设置默认地图路径,  # 使用当前工作目录的相对路径
            description='Path to the map file for localization (.pbstream file)'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # === 系统启动 ===
        fusion_bringup_launch,          # 基础硬件和双雷达融合
        ekf_launch,                     # EKF传感器融合（可选）
        cartographer_localization_node, # Cartographer定位
        occupancy_grid_node,            # Cartographer占用栅格节点
        localization_rviz_node,         # RViz可视化
    ])
