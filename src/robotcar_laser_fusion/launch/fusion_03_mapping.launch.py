import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, GroupAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition
from launch_ros.actions import Node, PushRosNamespace, SetRemap


def generate_launch_description():

    # 获取包路径
    pkg_robotcar_fusion = get_package_share_directory('robotcar_laser_fusion')
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    
    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    start_rviz = LaunchConfiguration('start_rviz')
    use_ekf = LaunchConfiguration('use_ekf')
    use_sensor_sync = LaunchConfiguration('use_sensor_sync')
    namespace = LaunchConfiguration('namespace')
    auto_start = LaunchConfiguration('auto_start')  #自启动参数
    
    fusion_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_01_bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 3. 启动传感器同步（可选） ===
    sensor_sync_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_nav, 'launch', 'nav_07_sensor_sync.launch.py')
        ),
        condition=IfCondition(use_sensor_sync),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'scan_topic': 'merged',  # 使用融合后的merged话题
            'namespace': namespace,
        }.items()
    )

    # === 4. 启动EKF传感器融合（可选） ===
    # 使用独立的EKF启动文件
    ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_02_ekf.launch.py')
        ),
        condition=IfCondition(use_ekf),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 5. 启动Cartographer SLAM建图 ===
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    cartographer_config_basename = 'cartographer_mapping.lua'
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'rviz.rviz'])

    cartographer_node = TimerAction(
        period=3.0,  
        actions=[
            GroupAction([
                PushRosNamespace(namespace=namespace),
                SetRemap("/tf", "tf"),
                SetRemap("/tf_static", "tf_static"),
                Node(
                    package='cartographer_ros',
                    executable='cartographer_node',
                    name='cartographer_node',
                    output='screen',
                    parameters=[
                        {'use_sim_time': use_sim_time},
                    ],
                    arguments=[
                        '-configuration_directory', cartographer_config_dir,
                        '-configuration_basename', cartographer_config_basename
                    ],
                    remappings=[
                        ('scan', 'merged'),  # 订阅融合后的激光雷达数据
                        ('imu', 'imu_sensor_broadcaster/imu'),  # 使用ROS2 Control的IMU数据
                        ('odom', 'odom'),  # 使用EKF融合后的高质量里程计数据
                    ]
                )
            ])
        ]
    )

    # === 6. 启动Cartographer占用栅格节点 ===
    occupancy_grid_node = TimerAction(
        period=5.0,  # 等待Cartographer节点完全启动并稳定
        actions=[
            GroupAction([
                PushRosNamespace(namespace=namespace),
                SetRemap("/tf", "tf"),
                SetRemap("/tf_static", "tf_static"),
                Node(
                    package='cartographer_ros',
                    executable='cartographer_occupancy_grid_node',
                    name='cartographer_occupancy_grid_node',
                    output='screen',
                    parameters=[{'use_sim_time': use_sim_time}],
                    arguments=['-resolution', '0.03', '-publish_period_sec', '1.0'],
                    remappings=[
                        ('map', 'carto_map'),  # {{ AURA-X: Fix - 输出原始地图到carto_map话题. }}
                    ]
                )
            ])
        ]
    )

    # {{ AURA-X: Add - 地图旋转节点，修正建图倾斜问题. Approval: 寸止(ID:map_rotation_integration). }}
    # === 7. 启动地图旋转节点 ===
    map_rotation_params_file = PathJoinSubstitution([pkg_robotcar_fusion, 'config', 'map_rotation_params.yaml'])

    map_rotation_node = TimerAction(
        period=7.0,  # 等待占用栅格节点启动并开始发布地图
        actions=[
            GroupAction([
                PushRosNamespace(namespace=namespace),
                SetRemap("/tf", "tf"),
                SetRemap("/tf_static", "tf_static"),
                Node(
                    package='robotcar_laser_fusion',
                    executable='map_rotation_node',
                    name='map_rotation_node',
                    output='screen',
                    parameters=[
                        {'use_sim_time': use_sim_time},
                        map_rotation_params_file  # {{ AURA-X: Add - 加载分辨率自适应参数. }}
                    ],
                    remappings=[
                        ('carto_map', 'carto_map'),  # 订阅原始Cartographer地图
                        ('map', 'map'),              # 发布修正后的地图
                        ('current_longest', 'current_longest'),  # 可视化最长线段
                    ]
                )
            ])
        ]
    )

    # === 7. 启动RViz可视化 ===
    mapping_rviz_node = TimerAction(
        period=1.0,  # 等待所有节点完全启动并稳定
        actions=[
            Node(
                package='rviz2',
                executable='rviz2',
                name='rviz2_mapping',
                # RViz不使用命名空间，在根命名空间运行以便访问所有话题
                arguments=['-d', rviz_config_file],
                parameters=[{'use_sim_time': use_sim_time}],
                condition=IfCondition(start_rviz),
                output='screen'
            )
        ]
    )

    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='False',  # 修复：使用Python布尔值格式
            description='Use simulation time if true'
        ),
        
        DeclareLaunchArgument(
            'start_rviz',
            default_value='True',
            description='Start RViz for mapping visualization'
        ),
        
        DeclareLaunchArgument(
            'use_ekf',
            default_value='True',
            description='Use EKF for sensor fusion'
        ),

        DeclareLaunchArgument(
            'use_sensor_sync',
            default_value='False',  # 修复：使用Python布尔值格式
            description='Use sensor synchronization for high-precision applications'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # === 系统启动 ===
        fusion_bringup_launch,          # 基础硬件和双雷达融合
        sensor_sync_launch,             # 传感器同步（可选）
        ekf_launch,                     # EKF传感器融合（可选）
        cartographer_node,              # Cartographer SLAM建图
        occupancy_grid_node,            # Cartographer占用栅格节点
        # map_rotation_node,              # {{ AURA-X: Add - 地图旋转节点，修正建图倾斜. }}
        mapping_rviz_node,              # RViz可视化
    ])
