cmake_minimum_required(VERSION 3.5)
project(robotcar_laser_fusion)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake_auto REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(OpenCV REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(cv_bridge REQUIRED)
ament_auto_find_build_dependencies()

# 生成服务接口
rosidl_generate_interfaces(${PROJECT_NAME}
  "srv/NavigateToWaypoint.srv"
  DEPENDENCIES std_msgs geometry_msgs
)

ament_auto_add_library(robotcar_laser_fusion_lib SHARED
  src/robotcar_laser_fusion.cpp)

rclcpp_components_register_node(robotcar_laser_fusion_lib
  PLUGIN "robotcar_laser_fusion::LaserFusionNode"
  EXECUTABLE robotcar_laser_fusion_node)

# 添加路网导航节点
add_executable(waypoint_navigator src/waypoint_navigator.cpp)
ament_target_dependencies(waypoint_navigator
  rclcpp
  geometry_msgs
  nav2_msgs
  rclcpp_action
  std_msgs
  std_srvs
)
target_link_libraries(waypoint_navigator yaml-cpp)

# 确保服务接口在编译节点之前生成
rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
target_link_libraries(waypoint_navigator "${cpp_typesupport_target}")

# {{ AURA-X: Add - 地图旋转节点编译配置. Approval: 寸止(ID:map_rotation_build). }}
# 添加地图旋转节点
add_executable(map_rotation_node src/map_rotation_node.cpp)
ament_target_dependencies(map_rotation_node
  rclcpp
  nav_msgs
  geometry_msgs
  visualization_msgs
  tf2
  tf2_ros
  cv_bridge
)
target_link_libraries(map_rotation_node ${OpenCV_LIBRARIES})
target_include_directories(map_rotation_node PRIVATE ${OpenCV_INCLUDE_DIRS} ${EIGEN3_INCLUDE_DIRS})

install(DIRECTORY include/ DESTINATION include)
install(DIRECTORY launch/ DESTINATION share/${PROJECT_NAME}/launch)
install(DIRECTORY bag/ DESTINATION share/${PROJECT_NAME}/bag)
install(DIRECTORY config/ DESTINATION share/${PROJECT_NAME}/config)

# 安装可执行文件
install(TARGETS waypoint_navigator map_rotation_node
  DESTINATION lib/${PROJECT_NAME}
)

# 安装Python脚本
install(PROGRAMS
  scripts/path_following_node.py
  scripts/waypoint_web_interface.py
  DESTINATION lib/${PROJECT_NAME}
)

if(BUILD_TESTING)
  #find_package(ament_lint_auto REQUIRED)
  #set(ament_cmake_copyright_FOUND TRUE)
  #set(ament_cmake_cpplint_FOUND TRUE)
  #set(ament_flake8_FOUND TRUE)
  #set(ament_pep257_FOUND TRUE)
  #set(ament_xmllint_FOUND TRUE)
  #ament_lint_auto_find_test_dependencies()
endif()

ament_auto_package()