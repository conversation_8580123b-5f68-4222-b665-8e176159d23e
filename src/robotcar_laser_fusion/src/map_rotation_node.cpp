//
// Created by tony on 2023/7/6.
// Adapted for ROS2 by AURA-X Agent
//

#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/srv/get_map.hpp>
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2/LinearMath/Quaternion.h>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <visualization_msgs/msg/marker.hpp>
#include <mutex>

namespace robotcar_laser_fusion {

static const double Degree2RadInv = M_PI / 180.0;

class MapRotationNode : public rclcpp::Node {
public:
    MapRotationNode();
    ~MapRotationNode();

private:
    // ROS2 组件
    std::mutex map_mutex_;
    rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr carto_map_sub_;
    rclcpp::Publisher<nav_msgs::msg::OccupancyGrid>::SharedPtr map_pub_;
    rclcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr marker_pub_;
    rclcpp::TimerBase::SharedPtr map_timer_;
    rclcpp::TimerBase::SharedPtr tf_timer_;
    std::shared_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;
    
    // 数据成员
    nav_msgs::msg::OccupancyGrid carto_map_;
    bool map_updated_;
    double map_x_, map_y_, map_theta_;

    // 回调函数
    void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
    void mapTimerCallback();
    void tfTimerCallback();

    // 核心处理函数
    void map2Img(cv::Mat& mat);
    void getTheta(const cv::Mat& mat);
    void rotMap(nav_msgs::msg::OccupancyGrid& map);
};

MapRotationNode::MapRotationNode() 
    : Node("map_rotation_node"), 
      map_x_(0.0), map_y_(0.0), map_theta_(0.0), map_updated_(false) {
    
    // {{ AURA-X: Add - ROS2地图旋转节点初始化. Approval: 寸止(ID:map_rotation_ros2). }}
    RCLCPP_INFO(this->get_logger(), "Map Rotation Node starting...");
    
    // 创建定时器（停止状态）
    map_timer_ = this->create_wall_timer(
        std::chrono::seconds(5), 
        std::bind(&MapRotationNode::mapTimerCallback, this)
    );
    map_timer_->cancel();  // 初始停止
    
    tf_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(20), 
        std::bind(&MapRotationNode::tfTimerCallback, this)
    );
    
    // 创建发布者和订阅者
    map_pub_ = this->create_publisher<nav_msgs::msg::OccupancyGrid>("/map", 1);
    marker_pub_ = this->create_publisher<visualization_msgs::msg::Marker>("/current_longest", 1);
    carto_map_sub_ = this->create_subscription<nav_msgs::msg::OccupancyGrid>(
        "carto_map", 1, 
        std::bind(&MapRotationNode::mapCallback, this, std::placeholders::_1)
    );
    
    // 创建TF广播器
    tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(this);
    
    RCLCPP_INFO(this->get_logger(), "Map Rotation Node initialized successfully");
}

MapRotationNode::~MapRotationNode() {
    if (map_timer_) {
        map_timer_->cancel();
    }
    if (tf_timer_) {
        tf_timer_->cancel();
    }
}

void MapRotationNode::mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg) {
    std::lock_guard<std::mutex> lock(map_mutex_);
    carto_map_ = *msg;
    map_updated_ = true;
    
    // 启动地图处理定时器
    if (map_timer_->is_canceled()) {
        map_timer_->reset();
    }
}

void MapRotationNode::mapTimerCallback() {
    if (!map_updated_) return;

    cv::Mat carto_mat;
    nav_msgs::msg::OccupancyGrid map = carto_map_;

    map2Img(carto_mat);
    getTheta(carto_mat);
    rotMap(map);
    map_pub_->publish(map);

    map_updated_ = false;
}

void MapRotationNode::tfTimerCallback() {
    geometry_msgs::msg::TransformStamped transform_stamped;
    
    transform_stamped.header.stamp = this->get_clock()->now();
    transform_stamped.header.frame_id = "map";
    transform_stamped.child_frame_id = "carto_map";
    
    transform_stamped.transform.translation.x = map_x_;
    transform_stamped.transform.translation.y = map_y_;
    transform_stamped.transform.translation.z = 0.0;
    
    tf2::Quaternion q;
    q.setRPY(0.0, 0.0, map_theta_);
    transform_stamped.transform.rotation.x = q.x();
    transform_stamped.transform.rotation.y = q.y();
    transform_stamped.transform.rotation.z = q.z();
    transform_stamped.transform.rotation.w = q.w();
    
    tf_broadcaster_->sendTransform(transform_stamped);
}

void MapRotationNode::map2Img(cv::Mat& mat) {
    std::lock_guard<std::mutex> lock(map_mutex_);

    auto sizex = carto_map_.info.width;
    auto sizey = carto_map_.info.height;
    mat = cv::Mat(sizey, sizex, CV_8U);

    for (uint32_t r = 0; r < sizey; ++r) {
        for (uint32_t c = 0; c < sizex; ++c) {
            if (carto_map_.data.at(c + (sizey - r - 1) * sizex) < 75) {
                mat.at<uchar>(r, c) = 0;
            } else {
                mat.at<uchar>(r, c) = static_cast<uchar>(carto_map_.data.at(c + (sizey - r - 1) * sizex));
            }
        }
    }
}

void MapRotationNode::getTheta(const cv::Mat& mat) {
    static double maxLength = 30.0; // 0.1 m/pixel
    cv::Mat mid;
    cv::Canny(mat, mid, 75, 99, 3);
    std::vector<cv::Vec4i> lines;
    cv::HoughLinesP(mid, lines, 1, CV_PI / 180, 30, maxLength * 0.9, 10);
    if (lines.empty()) return;

    auto max_ele = std::max_element(lines.begin(), lines.end(),
        [&](cv::Vec4i const& p1, cv::Vec4i const& p2) {
            return std::pow(p1[0] - p1[2], 2) + std::pow(p1[1] - p1[3], 2)
                   < std::pow(p2[0] - p2[2], 2) + std::pow(p2[1] - p2[3], 2);
        });

    auto x0 = static_cast<double>((*max_ele)[0]);
    auto x1 = static_cast<double>((*max_ele)[2]);
    auto y0 = static_cast<double>((*max_ele)[1]);
    auto y1 = static_cast<double>((*max_ele)[3]);
    auto length = hypot(x1 - x0, y1 - y0);
    auto theta = atan2(y1 - y0, x1 - x0);

    RCLCPP_INFO(this->get_logger(), "[rotate map] Scan map got %.2f m length and %.2f rad theta.", length, theta);

    if (length > 1.1 * maxLength || fabs(theta - map_theta_) > Degree2RadInv) {
        RCLCPP_INFO(this->get_logger(), "[rotate map] Update global theta.");
        maxLength = std::max(maxLength, length);
        map_theta_ = theta;
        if (fabs(map_theta_) < Degree2RadInv) {
            map_theta_ = 0.0;
        }

        // 发布可视化标记
        visualization_msgs::msg::Marker line_marker;
        line_marker.type = visualization_msgs::msg::Marker::ARROW;
        line_marker.header.frame_id = "carto_map";
        line_marker.header.stamp = this->get_clock()->now();
        line_marker.ns = "current_longest";
        line_marker.action = visualization_msgs::msg::Marker::ADD;
        line_marker.color.a = 1.0;
        line_marker.color.r = 0.0;
        line_marker.color.g = 0.0;
        line_marker.color.b = 1.0;
        line_marker.id = 0;
        line_marker.scale.x = 0.05;
        line_marker.scale.y = 0.08;

        geometry_msgs::msg::Point p;
        p.x = carto_map_.info.origin.position.x + carto_map_.info.resolution * x0;
        p.y = carto_map_.info.origin.position.y + carto_map_.info.resolution * (carto_map_.info.height - 1 - y0);
        p.z = 0;
        line_marker.points.emplace_back(p);
        p.x = carto_map_.info.origin.position.x + carto_map_.info.resolution * x1;
        p.y = carto_map_.info.origin.position.y + carto_map_.info.resolution * (carto_map_.info.height - 1 - y1);
        line_marker.points.emplace_back(p);
        marker_pub_->publish(line_marker);
    }
}

void MapRotationNode::rotMap(nav_msgs::msg::OccupancyGrid& map) {
    if (fabs(map_theta_) < Degree2RadInv) {
        map = carto_map_;
        map.header.frame_id = "map";
        map_x_ = 0.0;
        map_y_ = 0.0;
        map_theta_ = 0.0;
        return;
    }

    std::lock_guard<std::mutex> lock(map_mutex_);
    auto cos_p = cos(map_theta_);
    auto sin_p = sin(map_theta_);
    auto cos_n = cos_p;
    auto sin_n = -sin_p;
    auto resolution = carto_map_.info.resolution;
    auto width_cell_old = carto_map_.info.width;
    auto height_cell_old = carto_map_.info.height;
    auto width_cell = static_cast<unsigned int>(fabs(width_cell_old * cos_p) + fabs(height_cell_old * sin_p));
    auto height_cell = static_cast<unsigned int>(fabs(width_cell_old * sin_p) + fabs(height_cell_old * cos_p));
    auto width_old = static_cast<float>(width_cell_old) * resolution;
    auto height_old = static_cast<float>(height_cell_old) * resolution;
    auto width = static_cast<float>(width_cell) * resolution;
    auto height = static_cast<float>(height_cell) * resolution;
    auto center_x = carto_map_.info.origin.position.x + width_old / 2;
    auto center_y = carto_map_.info.origin.position.y + height_old / 2;

    map.header.stamp = this->get_clock()->now();
    map.header.frame_id = "map";
    map.info.width = width_cell;
    map.info.height = height_cell;
    map.info.resolution = resolution;
    map.info.origin.position.x = center_x - width / 2;
    map.info.origin.position.y = center_y - height / 2;
    map.info.origin.position.z = 0.0;
    map.info.origin.orientation.x = 0.0;
    map.info.origin.orientation.y = 0.0;
    map.info.origin.orientation.z = 0.0;
    map.info.origin.orientation.w = 1.0;
    map.data.resize(width_cell * height_cell);

    map_x_ = center_x + (0.0 - center_x) * cos_p - (0.0 - center_y) * sin_p;
    map_y_ = center_y + (0.0 - center_x) * sin_p + (0.0 - center_y) * cos_p;

    for (unsigned int i = 0; i < width_cell; ++i) {
        for (unsigned int j = 0; j < height_cell; ++j) {
            auto wx = map.info.origin.position.x + static_cast<float>(i) * resolution;
            auto wy = map.info.origin.position.y + static_cast<float>(j) * resolution;
            auto wx_old = center_x + (wx - center_x) * cos_n - (wy - center_y) * sin_n;
            auto wy_old = center_y + (wx - center_x) * sin_n + (wy - center_y) * cos_n;
            auto ii = floor((wx_old - carto_map_.info.origin.position.x) / resolution + 0.5);
            auto jj = floor((wy_old - carto_map_.info.origin.position.y) / resolution + 0.5);
            if (ii < 0 || ii >= carto_map_.info.width || jj < 0 || jj >= carto_map_.info.height) {
                map.data.at(i + j * map.info.width) = -1;
            } else {
                map.data.at(i + j * map.info.width) = carto_map_.data.at(static_cast<unsigned long>(ii + jj * carto_map_.info.width));
            }
        }
    }
}

} // namespace robotcar_laser_fusion

int main(int argc, char** argv) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<robotcar_laser_fusion::MapRotationNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
