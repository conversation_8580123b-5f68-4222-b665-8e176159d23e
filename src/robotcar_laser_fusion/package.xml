<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robotcar_laser_fusion</name>
  <version>1.0.0</version>
  <description>RobotCar dual laser fusion system for SLAM and navigation. Merges dual lidar scans into unified laser data for localization and mapping.</description>
  <maintainer email="<EMAIL>">RobotCar Team</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_auto</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>

  <depend>message_filters</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>libpcl-all-dev</depend>
  <depend>laser_geometry</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>nav2_msgs</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>rclpy</depend>
  <depend>rclcpp_action</depend>
  <depend>nav2_bringup</depend>
  <depend>nav2_regulated_pure_pursuit_controller</depend>
  <depend>yaml-cpp-vendor</depend>
  <depend>cv_bridge</depend>
  <depend>image_transport</depend>
  <depend>opencv</depend>
  <depend>eigen3_cmake_module</depend>
  <depend>visualization_msgs</depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_cpplint</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>ament_xmllint</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
